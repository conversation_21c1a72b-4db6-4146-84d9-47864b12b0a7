# chatStreamSSE 逻辑重构需求

## 背景

当前的 `chatStreamSSE` 函数只能进行简单的意图识别和内容返回，缺乏实际的任务执行能力。为了实现真正的智能任务助手功能，需要重构该函数，使其能够根据用户意图调用相应的工具函数来执行具体的任务操作。

这个重构将使 AI 助理从单纯的对话工具升级为能够实际执行任务管理操作的智能助手，大大提升用户体验和功能实用性。

## 需求

### 功能需求

#### 1. 意图分类与处理

- **简化意图分类**：将原有的 `create_task`、`find_task` 合并为统一的 `task` 意图
- **两种意图类型**：系统只需识别 `task` 和 `chat` 两种意图类型
- **任务模式增强**：对于 `task` 意图，需要进行实际的工具函数调用
- **闲聊模式保持**：对于 `chat` 意图，保持现有的纯对话逻辑

#### 2. 工具函数集成

- **任务管理工具**：集成滴答清单 API 的任务相关函数
- **项目管理工具**：集成滴答清单 API 的项目相关函数
- **执行计划生成**：根据用户意图自动生成具体的执行步骤

#### 3. 流式响应优化

- **保持 SSE 机制**：确保流式推送功能正常工作
- **增加执行状态推送**：推送工具函数执行状态和结果
- **错误处理增强**：完善工具函数调用的错误处理机制

### 非功能需求

- **性能要求**：工具函数调用不应显著影响响应速度
- **可靠性要求**：确保工具函数调用失败时有合适的降级处理
- **可扩展性要求**：架构设计应便于后续添加更多工具函数

## 技术方案

### 实现思路

1. **意图识别阶段**：AI 识别用户输入为 `task` 或 `chat` 两种意图之一
2. **计划生成阶段**：对于 `task` 意图，根据用户输入生成具体的执行计划
3. **工具调用阶段**：按照计划依次调用相应的工具函数
4. **结果整合阶段**：将工具函数执行结果整合并通过 SSE 推送给前端

### 架构设计

```mermaid
graph TD
    A[用户输入] --> B[意图识别]
    B --> C{意图类型}
    C -->|chat| D[直接AI对话]
    C -->|task| E[分析任务需求]
    E --> F[生成执行计划]
    F --> G[调用工具函数]
    G --> H[整合执行结果]
    H --> I[SSE流式推送]
    D --> I

    subgraph "工具注册系统"
        J[工具注册表] --> K[动态提示词生成]
        K --> B
        J --> L[工具函数映射]
        L --> G
    end

    subgraph "执行计划生成"
        E --> M[解析用户意图]
        M --> N[选择合适工具]
        N --> O[准备工具参数]
        O --> F
    end

    subgraph "工具函数库"
        P[任务管理工具]
        Q[项目管理工具]
        R[其他工具...]
    end

    L --> P
    L --> Q
    L --> R
```

### 工具注册系统

#### 工具注册数据结构

每个工具必须按照以下数据结构进行注册：

```javascript
const toolRegistry = {
  toolName: {
    name: '工具名称', // 唯一标识符
    description: '功能描述', // 工具的主要功能说明
    usage: '使用场景描述', // 什么情况下使用这个工具
    parameters: {
      paramName: {
        type: 'string|number|boolean|object|array',
        required: true | false,
        default: '默认值',
        description: '参数说明',
      },
    },
    cloudFunction: 'dida-todo', // 对应的云函数名
    method: 'methodName', // 云函数中的方法名
  },
}
```

#### 工具注册示例

```javascript
// 工具注册配置
const TOOL_REGISTRY = {
  createTask: {
    name: '创建任务',
    description: '在滴答清单中创建一个新任务',
    usage: '当用户想要添加、创建、新建任务时使用',
    parameters: {
      taskData: {
        type: 'object',
        required: true,
        description: '任务数据对象',
        properties: {
          title: {
            type: 'string',
            required: true,
            description: '任务标题',
          },
          content: {
            type: 'string',
            required: false,
            default: '',
            description: '任务详细内容',
          },
          projectId: {
            type: 'string',
            required: false,
            description: '所属项目 ID，不指定则创建到默认项目',
          },
        },
      },
    },
    cloudFunction: 'dida-todo',
    method: 'createTask',
  },

  getTasks: {
    name: '获取任务列表',
    description: '查询滴答清单中的任务',
    usage: '当用户想要查看、搜索、查询任务时使用',
    parameters: {
      projectId: {
        type: 'string',
        required: false,
        description: '项目 ID，不指定则获取所有项目的任务',
      },
      completed: {
        type: 'boolean',
        required: false,
        default: false,
        description: '是否获取已完成的任务',
      },
      limit: {
        type: 'number',
        required: false,
        default: 50,
        description: '返回任务数量限制，最大 100',
      },
    },
    cloudFunction: 'dida-todo',
    method: 'getTasks',
  },

  // ... 其他工具注册
}
```

#### 动态提示词生成

工具注册信息将动态添加到 AI 提示词中：

```javascript
function generateToolPrompt(toolRegistry) {
  let toolPrompt = '你可以使用以下工具来帮助用户完成任务：\n\n'

  for (const [toolKey, tool] of Object.entries(toolRegistry)) {
    toolPrompt += `**${tool.name}** (${toolKey})\n`
    toolPrompt += `- 功能：${tool.description}\n`
    toolPrompt += `- 使用场景：${tool.usage}\n`
    toolPrompt += `- 参数：\n`

    for (const [paramName, param] of Object.entries(tool.parameters)) {
      const required = param.required ? '必需' : '可选'
      const defaultValue = param.default ? ` (默认: ${param.default})` : ''
      toolPrompt += `  - ${paramName} (${param.type}, ${required}${defaultValue}): ${param.description}\n`
    }
    toolPrompt += '\n'
  }

  return toolPrompt
}
```

### 可用工具函数

基于工具注册系统，当前可用的工具函数包括：

**任务管理相关：**

- `createTask` - 创建任务
- `getTasks` - 获取任务列表
- `getTask` - 获取任务详情
- `updateTask` - 更新任务
- `deleteTask` - 删除任务
- `completeTask` - 完成任务

**项目管理相关：**

- `getProjects` - 获取项目列表
- `getProject` - 获取项目详情
- `getProjectData` - 获取项目及其数据
- `createProject` - 创建项目
- `updateProject` - 更新项目
- `deleteProject` - 删除项目

### 技术栈与约束

- **云函数框架**：uniCloud
- **AI 模型**：豆包 (doubao-seed-1-6-250615)
- **流式推送**：SSE (Server-Sent Events)
- **API 集成**：滴答清单开放 API
- **性能约束**：单次工具函数调用不超过 10 秒
- **并发约束**：避免同时调用多个可能冲突的工具函数

### 实现细节

#### 1. 执行计划生成逻辑

执行计划的核心是根据用户输入分析需要调用哪些工具，并为每个工具准备相应的入参。

##### 执行计划数据结构

```javascript
// 执行计划数据结构定义
const executionPlan = {
  planId: 'uuid', // 计划唯一标识
  userInput: '用户原始输入',
  steps: [
    {
      stepId: 'uuid', // 步骤唯一标识
      toolName: 'createTask', // 工具名称（对应注册表中的 key）
      description: '创建新任务：学习 JavaScript', // 步骤描述
      parameters: {
        // 工具调用参数
        taskData: {
          title: '学习 JavaScript',
          content: '每天学习 1 小时 JavaScript 基础知识',
          projectId: null,
        },
      },
      dependencies: [], // 依赖的前置步骤 ID
      status: 'pending', // pending | executing | completed | failed
    },
  ],
  totalSteps: 1,
  currentStep: 0,
  status: 'pending', // pending | executing | completed | failed
}
```

##### 执行计划生成算法

```javascript
// 执行计划生成函数
async function generateExecutionPlan(userInput, toolRegistry) {
  // 1. 使用 AI 分析用户输入，识别需要执行的操作
  const analysisPrompt = `
分析用户输入："${userInput}"
根据以下可用工具，生成执行计划：
${generateToolPrompt(toolRegistry)}

请按照以下 JSON 格式返回执行计划：
{
  "analysis": "用户意图分析",
  "steps": [
    {
      "toolName": "工具名称",
      "description": "步骤描述",
      "parameters": { "参数对象" },
      "reasoning": "选择此工具的原因"
    }
  ]
}
`

  // 2. 调用 AI 获取分析结果
  const aiResponse = await callAI(analysisPrompt)
  const planData = JSON.parse(aiResponse)

  // 3. 验证和完善执行计划
  const executionPlan = {
    planId: generateUUID(),
    userInput: userInput,
    steps: planData.steps.map((step, index) => ({
      stepId: generateUUID(),
      toolName: step.toolName,
      description: step.description,
      parameters: validateAndCompleteParameters(step.parameters, toolRegistry[step.toolName]),
      dependencies: [], // 简单实现，后续可扩展依赖关系
      status: 'pending',
    })),
    totalSteps: planData.steps.length,
    currentStep: 0,
    status: 'pending',
  }

  return executionPlan
}

// 参数验证和补全函数
function validateAndCompleteParameters(inputParams, toolConfig) {
  const validatedParams = {}

  for (const [paramName, paramConfig] of Object.entries(toolConfig.parameters)) {
    if (paramConfig.required && !inputParams[paramName]) {
      throw new Error(`缺少必需参数：${paramName}`)
    }

    validatedParams[paramName] = inputParams[paramName] || paramConfig.default
  }

  return validatedParams
}
```

#### 2. 工具函数调用逻辑

```javascript
// 执行计划执行函数
async function executeTaskPlan(executionPlan, sseChannel) {
  try {
    // 推送执行计划
    await sseChannel.write({
      type: 'execution_plan',
      plan: executionPlan,
      timestamp: Date.now(),
    })

    executionPlan.status = 'executing'

    for (let i = 0; i < executionPlan.steps.length; i++) {
      const step = executionPlan.steps[i]
      executionPlan.currentStep = i

      // 推送当前执行步骤
      await sseChannel.write({
        type: 'execution_step',
        step: {
          stepId: step.stepId,
          description: step.description,
          toolName: step.toolName,
        },
        timestamp: Date.now(),
      })

      step.status = 'executing'

      try {
        // 调用工具函数
        const result = await callToolFunction(step.toolName, step.parameters)

        step.status = 'completed'

        // 推送步骤执行结果
        await sseChannel.write({
          type: 'step_result',
          stepId: step.stepId,
          result: result,
          timestamp: Date.now(),
        })
      } catch (error) {
        step.status = 'failed'
        step.error = error.message

        await sseChannel.write({
          type: 'step_error',
          stepId: step.stepId,
          error: error.message,
          timestamp: Date.now(),
        })

        // 根据错误处理策略决定是否继续执行
        if (step.critical !== false) {
          throw error // 关键步骤失败则终止整个计划
        }
      }
    }

    executionPlan.status = 'completed'

    // 推送执行完成
    await sseChannel.write({
      type: 'execution_complete',
      plan: executionPlan,
      timestamp: Date.now(),
    })
  } catch (error) {
    executionPlan.status = 'failed'
    executionPlan.error = error.message

    await sseChannel.write({
      type: 'execution_failed',
      plan: executionPlan,
      error: error.message,
      timestamp: Date.now(),
    })
  }
}

// 工具函数调用封装
async function callToolFunction(toolName, parameters) {
  const toolConfig = TOOL_REGISTRY[toolName]
  if (!toolConfig) {
    throw new Error(`未找到工具：${toolName}`)
  }

  // 调用对应的云函数
  const cloudFunction = uniCloud.importObject(toolConfig.cloudFunction)
  return await cloudFunction[toolConfig.method](parameters)
}
```

#### 3. SSE 消息类型扩展

基于工具注册和执行计划系统，扩展以下 SSE 消息类型：

- `execution_plan` - 推送完整的执行计划
- `execution_step` - 推送当前执行步骤信息
- `step_result` - 推送步骤执行成功结果
- `step_error` - 推送步骤执行错误信息
- `execution_complete` - 推送整体执行完成
- `execution_failed` - 推送整体执行失败

#### 4. 完整的 chatStreamSSE 重构示例

```javascript
async function chatStreamSSE(params) {
  let { message, messages: history_records = [], model = 'doubao-seed-1-6-250615', channel } = params

  try {
    const sseChannel = uniCloud.deserializeSSEChannel(channel)

    // 1. 生成包含工具信息的系统提示词
    const toolPrompt = generateToolPrompt(TOOL_REGISTRY)
    const system = `你是一个专业的任务助手。请分析用户输入并判断意图类型：
- task: 需要执行具体任务操作（创建、查询、更新、删除任务或项目）
- chat: 一般闲聊对话

${toolPrompt}

对于 task 类型，请按照以下格式返回：
「意图类型」：task
「执行计划」：[详细的 JSON 格式执行计划]

对于 chat 类型，请按照以下格式返回：
「意图类型」：chat
「回复内容」：[你的回复内容]`

    // 2. 调用 AI 进行意图识别
    const openai = new OpenAI(doubaoParams)
    const messages = [{ role: 'system', content: system }, ...history_records]
    if (message) messages.push({ role: 'user', content: message })

    const streamResponse = await openai.chat.completions.create({
      messages,
      model: model,
      stream: true,
      timeout: 300000,
    })

    let fullContent = ''
    let intentType = null
    let executionPlan = null

    // 3. 处理流式响应
    for await (const chunk of streamResponse) {
      const content = chunk.choices[0]?.delta?.content || ''
      if (content) {
        fullContent += content

        // 检测意图类型
        if (!intentType) {
          const typeMatch = /「意图类型」：(task|chat)/.exec(fullContent)
          if (typeMatch) {
            intentType = typeMatch[1]
            await sseChannel.write({
              type: 'intent_type',
              intentType: intentType,
              timestamp: Date.now(),
            })
          }
        }

        // 如果是 task 类型，解析执行计划
        if (intentType === 'task' && !executionPlan) {
          const planMatch = /「执行计划」：([\s\S]*)/.exec(fullContent)
          if (planMatch) {
            try {
              executionPlan = JSON.parse(planMatch[1])
              // 执行任务计划
              await executeTaskPlan(executionPlan, sseChannel)
              return {
                errCode: 0,
                errMsg: 'success',
                data: { type: 'task_executed', executionPlan },
              }
            } catch (error) {
              console.error('执行计划解析或执行失败：', error)
            }
          }
        }

        // 如果是 chat 类型，继续流式推送对话内容
        if (intentType === 'chat') {
          await sseChannel.write({
            type: 'chat_content',
            content: content,
            timestamp: Date.now(),
          })
        }
      }
    }

    // 4. 发送结束消息
    await sseChannel.end({
      type: 'end',
      content: fullContent,
      intentType: intentType,
      timestamp: Date.now(),
    })

    return {
      errCode: 0,
      errMsg: 'success',
      data: {
        type: 'stream_complete',
        content: fullContent,
        intentType: intentType,
      },
    }
  } catch (error) {
    console.error('chatStreamSSE 错误：', error)
    // 错误处理逻辑...
    return {
      errCode: 'API_ERROR',
      errMsg: error.message || '调用失败',
    }
  }
}
```

## 风险评估

### 假设与未知因素

- **假设**：滴答清单 API 稳定可用，响应时间在可接受范围内
- **假设**：用户输入的任务描述足够清晰，能够准确识别意图
- **未知因素**：复杂任务场景下的工具函数组合调用效果
- **未知因素**：高并发情况下的系统稳定性

### 潜在风险

1. **API 调用失败风险**

   - **风险描述**：滴答清单 API 不可用或响应超时
   - **应对策略**：实现重试机制和降级处理，API 失败时回退到纯对话模式

2. **意图识别错误风险**

   - **风险描述**：AI 错误识别用户意图，将任务操作误判为闲聊或反之
   - **应对策略**：优化意图识别提示词，增加确认机制，对于重要操作（如删除）需要用户二次确认

3. **性能影响风险**

   - **风险描述**：工具函数调用导致响应时间过长
   - **应对策略**：设置合理的超时时间，优化工具函数调用逻辑

4. **数据一致性风险**
   - **风险描述**：多个工具函数调用之间可能存在数据不一致
   - **应对策略**：设计合理的事务处理机制，确保操作的原子性

### 解决方案

- **监控告警**：添加工具函数调用的监控和告警机制
- **日志记录**：详细记录所有工具函数调用的参数和结果
- **测试覆盖**：编写全面的单元测试和集成测试
- **渐进式发布**：先在小范围用户中测试，逐步扩大使用范围
