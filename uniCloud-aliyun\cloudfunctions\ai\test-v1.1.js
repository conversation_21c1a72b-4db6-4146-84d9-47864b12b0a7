// V1.1 功能测试脚本
// 测试执行上下文管理器、执行计划生成器和工具调用引擎

// 由于类定义在主文件中，我们需要直接复制类定义来进行测试
// 这是一个简化的测试版本

// 简化的 ExecutionContextManager 用于测试
class ExecutionContextManager {
  constructor(sessionId, userInput) {
    this.sessionId = sessionId
    this.userInput = userInput
    this.stepResults = new Map()
    this.contextData = new Map()
    this.metadata = {
      startTime: Date.now(),
      currentStep: 0,
      totalSteps: 0
    }
  }

  setStepResult(stepId, result, metadata = {}) {
    this.stepResults.set(stepId, {
      result,
      metadata: {
        ...metadata,
        timestamp: Date.now(),
        stepIndex: this.metadata.currentStep
      }
    })
    this.extractContextData(stepId, result)
  }

  getStepResult(stepId) {
    return this.stepResults.get(stepId)?.result
  }

  setContextData(key, value) {
    this.contextData.set(key, value)
  }

  getContextData(key) {
    return this.contextData.get(key)
  }

  extractContextData(stepId, result) {
    if (result.data && Array.isArray(result.data)) {
      const targetProject = this.findTargetProject(result.data)
      if (targetProject) {
        this.setContextData('targetProject', targetProject)
      }
    }

    if (result.tasks && Array.isArray(result.tasks)) {
      this.setContextData('taskCount', result.tasks.length)
      this.setContextData('uncompletedTasks', result.tasks.filter(t => !t.completed))
    }
  }

  findTargetProject(projects) {
    const userInput = this.userInput.toLowerCase()
    const keywords = this.extractKeywords(userInput)

    const scored = projects.map(project => ({
      project,
      score: this.calculateMatchScore(project, keywords)
    }))

    scored.sort((a, b) => b.score - a.score)
    return scored.length > 0 && scored[0].score > 0 ? scored[0].project : null
  }

  extractKeywords(input) {
    const keywords = []
    const projectMatches = input.match(/(\w+)项目|(\w+)project/gi)
    if (projectMatches) {
      projectMatches.forEach(match => {
        const keyword = match.replace(/项目|project/gi, '')
        if (keyword) keywords.push(keyword.toLowerCase())
      })
    }
    return keywords
  }

  calculateMatchScore(project, keywords) {
    let score = 0
    const projectName = project.name.toLowerCase()
    keywords.forEach(keyword => {
      if (projectName.includes(keyword)) {
        score += 1
      }
    })
    return score
  }
}

// 简化的 SimpleExecutionPlanner 用于测试
class SimpleExecutionPlanner {
  static async generatePlan(userInput, intentType) {
    const executionPlan = {
      planId: this.generateUUID(),
      userInput: userInput,
      intentType: intentType,
      steps: [],
      totalSteps: 0,
      status: 'pending',
      startTime: Date.now()
    }

    switch (intentType) {
      case 'find_task':
        if (userInput.includes('项目') || userInput.includes('project')) {
          executionPlan.steps = [
            {
              stepId: this.generateUUID(),
              toolName: 'getProjects',
              description: '获取项目列表',
              parameters: { filter: this.extractProjectKeyword(userInput) },
              dependencies: [],
              status: 'pending'
            },
            {
              stepId: this.generateUUID(),
              toolName: 'getTasks',
              description: '获取项目下的任务',
              parameters: {
                projectId: '$context.targetProject.id',
                completed: false
              },
              dependencies: ['step1'],
              status: 'pending'
            }
          ]
        } else {
          executionPlan.steps = [
            {
              stepId: this.generateUUID(),
              toolName: 'getTasks',
              description: '获取任务列表',
              parameters: { completed: false },
              dependencies: [],
              status: 'pending'
            }
          ]
        }
        break
      case 'create_task':
        break
      default:
        break
    }

    executionPlan.totalSteps = executionPlan.steps.length
    return executionPlan
  }

  static extractProjectKeyword(input) {
    const matches = input.match(/(\w+)项目|(\w+)project/gi)
    if (matches && matches.length > 0) {
      return matches[0].replace(/项目|project/gi, '')
    }
    return ''
  }

  static generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0
      const v = c == 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  }
}

// 简化的 simulateToolCall 用于测试
async function simulateToolCall(toolName, parameters) {
  await new Promise(resolve => setTimeout(resolve, 100)) // 减少延迟用于测试

  switch (toolName) {
    case 'getProjects':
      return {
        success: true,
        data: [
          { id: 'proj-1', name: 'OKR项目', description: '目标管理项目' },
          { id: 'proj-2', name: '日常任务', description: '日常工作任务' }
        ],
        metadata: { total: 2, filtered: parameters.filter ? 1 : 2 }
      }

    case 'getTasks':
      return {
        success: true,
        tasks: [
          { id: 'task-1', title: '制定Q1目标', completed: false, projectId: parameters.projectId || 'proj-1' },
          { id: 'task-2', title: '更新KR进度', completed: false, projectId: parameters.projectId || 'proj-1' }
        ],
        metadata: { total: 2, projectId: parameters.projectId }
      }

    default:
      throw new Error(`未知的工具：${toolName}`)
  }
}

// 测试用例 1: 执行上下文管理器测试
async function testExecutionContextManager() {
  console.log('=== 测试执行上下文管理器 ===')
  
  const context = new ExecutionContextManager('session-1', '查看okr项目的任务')
  
  // 测试项目结果提取
  const projectResult = {
    success: true,
    data: [
      { id: 'proj-1', name: 'OKR项目', description: '目标管理项目' },
      { id: 'proj-2', name: '日常任务', description: '日常工作任务' }
    ]
  }
  
  context.setStepResult('step-1', projectResult)
  
  console.log('上下文数据:', Array.from(context.contextData.keys()))
  console.log('目标项目:', context.getContextData('targetProject'))
  
  // 测试任务结果提取
  const taskResult = {
    success: true,
    tasks: [
      { id: 'task-1', title: '制定Q1目标', completed: false },
      { id: 'task-2', title: '更新KR进度', completed: true }
    ]
  }
  
  context.setStepResult('step-2', taskResult)
  console.log('任务统计:', context.getContextData('taskCount'))
  console.log('未完成任务:', context.getContextData('uncompletedTasks'))
}

// 测试用例 2: 执行计划生成测试
async function testExecutionPlanner() {
  console.log('\n=== 测试执行计划生成器 ===')
  
  // 测试查找任务计划（包含项目关键词）
  const plan1 = await SimpleExecutionPlanner.generatePlan('查看okr项目的任务', 'find_task')
  console.log('项目任务查询计划:')
  console.log('- 计划ID:', plan1.planId)
  console.log('- 总步骤数:', plan1.totalSteps)
  console.log('- 步骤详情:', plan1.steps.map(s => ({ toolName: s.toolName, description: s.description })))
  
  // 测试查找任务计划（不包含项目关键词）
  const plan2 = await SimpleExecutionPlanner.generatePlan('查看我的任务', 'find_task')
  console.log('\n直接任务查询计划:')
  console.log('- 计划ID:', plan2.planId)
  console.log('- 总步骤数:', plan2.totalSteps)
  console.log('- 步骤详情:', plan2.steps.map(s => ({ toolName: s.toolName, description: s.description })))
  
  // 测试聊天类型（不生成计划）
  const plan3 = await SimpleExecutionPlanner.generatePlan('你好', 'chat')
  console.log('\n聊天类型计划:')
  console.log('- 总步骤数:', plan3.totalSteps)
}

// 测试用例 3: 模拟工具调用测试
async function testSimulateToolCall() {
  console.log('\n=== 测试模拟工具调用 ===')
  
  try {
    // 测试获取项目
    console.log('测试 getProjects:')
    const projectsResult = await simulateToolCall('getProjects', { filter: 'okr' })
    console.log('- 成功:', projectsResult.success)
    console.log('- 项目数量:', projectsResult.data?.length)
    console.log('- 项目列表:', projectsResult.data?.map(p => p.name))
    
    // 测试获取任务
    console.log('\n测试 getTasks:')
    const tasksResult = await simulateToolCall('getTasks', { projectId: 'proj-1', completed: false })
    console.log('- 成功:', tasksResult.success)
    console.log('- 任务数量:', tasksResult.tasks?.length)
    console.log('- 任务列表:', tasksResult.tasks?.map(t => t.title))
    
    // 测试未知工具
    console.log('\n测试未知工具:')
    try {
      await simulateToolCall('unknownTool', {})
    } catch (error) {
      console.log('- 预期错误:', error.message)
    }
    
  } catch (error) {
    console.error('工具调用测试失败:', error.message)
  }
}

// 测试用例 4: 关键词提取测试
function testKeywordExtraction() {
  console.log('\n=== 测试关键词提取 ===')
  
  const testCases = [
    '查看okr项目的任务',
    '显示project任务',
    '我想看看日常项目',
    '查看所有任务'
  ]
  
  testCases.forEach(input => {
    const keyword = SimpleExecutionPlanner.extractProjectKeyword(input)
    console.log(`输入: "${input}" -> 关键词: "${keyword}"`)
  })
}

// 运行所有测试
async function runAllTests() {
  console.log('开始 V1.1 功能测试...\n')
  
  try {
    await testExecutionContextManager()
    await testExecutionPlanner()
    await testSimulateToolCall()
    testKeywordExtraction()
    
    console.log('\n✅ 所有测试完成！')
  } catch (error) {
    console.error('\n❌ 测试失败:', error.message)
    console.error(error.stack)
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runAllTests()
}

module.exports = {
  testExecutionContextManager,
  testExecutionPlanner,
  testSimulateToolCall,
  testKeywordExtraction,
  runAllTests
}
