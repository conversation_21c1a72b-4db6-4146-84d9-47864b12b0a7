// V1.2 动态参数解析功能实现
// 实现动态参数解析机制，支持复杂的参数引用和数据筛选
// 替换V1.1的模拟工具调用为真实的云函数调用

// 引入必要的依赖
const OpenAI = require('openai')

// 豆包AI配置参数
const doubaoParams = {
  baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  apiKey: 'a8b8b8b8-b8b8-b8b8-b8b8-b8b8b8b8b8b8',
  timeout: 30000,
}

// V1.2 新增：工具注册表
const TOOL_REGISTRY = {
  getProjects: {
    cloudFunction: 'dida-todo',
    method: 'getProjects',
    description: '获取项目列表',
    parameters: {
      filter: { type: 'string', required: false, description: '项目名称过滤关键词' },
    },
    metadata: {
      estimatedTime: 1500,
      category: 'query',
    },
  },
  getTasks: {
    cloudFunction: 'dida-todo',
    method: 'getTasks',
    description: '获取任务列表',
    parameters: {
      projectId: { type: 'string', required: false, description: '项目ID' },
      completed: { type: 'boolean', required: false, description: '是否获取已完成任务' },
      limit: { type: 'number', required: false, description: '返回数量限制' },
    },
    metadata: {
      estimatedTime: 2000,
      category: 'query',
    },
  },
  createTask: {
    cloudFunction: 'dida-todo',
    method: 'createTask',
    description: '创建任务',
    parameters: {
      taskData: { type: 'object', required: true, description: '任务数据' },
    },
    metadata: {
      estimatedTime: 2500,
      category: 'action',
    },
  },
  getProject: {
    cloudFunction: 'dida-todo',
    method: 'getProject',
    description: '获取项目详情',
    parameters: {
      projectId: { type: 'string', required: true, description: '项目ID' },
    },
    metadata: {
      estimatedTime: 1500,
      category: 'query',
    },
  },
}

// V1.2 新增：参数验证器
class ParameterValidator {
  static validate(toolName, parameters) {
    const toolConfig = TOOL_REGISTRY[toolName]
    if (!toolConfig) {
      throw new Error(`未知的工具：${toolName}`)
    }

    const validated = {}
    const toolParams = toolConfig.parameters || {}

    // 检查必需参数
    for (const [paramName, paramConfig] of Object.entries(toolParams)) {
      if (paramConfig.required && (parameters[paramName] === undefined || parameters[paramName] === null)) {
        throw new Error(`缺少必需参数：${paramName}`)
      }
    }

    // 验证参数类型
    for (const [paramName, value] of Object.entries(parameters)) {
      if (value !== undefined && value !== null) {
        const paramConfig = toolParams[paramName]
        if (paramConfig) {
          validated[paramName] = this.validateParameterType(paramName, value, paramConfig.type)
        } else {
          // 允许额外参数，但记录警告
          console.warn(`工具 ${toolName} 收到未定义的参数：${paramName}`)
          validated[paramName] = value
        }
      }
    }

    return validated
  }

  static validateParameterType(paramName, value, expectedType) {
    switch (expectedType) {
      case 'string':
        return String(value)
      case 'number':
        const num = Number(value)
        if (isNaN(num)) {
          throw new Error(`参数 ${paramName} 必须是数字`)
        }
        return num
      case 'boolean':
        if (typeof value === 'boolean') return value
        if (typeof value === 'string') {
          return value.toLowerCase() === 'true'
        }
        return Boolean(value)
      case 'object':
        if (typeof value !== 'object' || value === null) {
          throw new Error(`参数 ${paramName} 必须是对象`)
        }
        return value
      default:
        return value
    }
  }
}

// V1.2 新增：动态参数解析器
class DynamicParameterResolver {
  static async resolveParameters(step, context) {
    const { parameters, dependencies } = step
    const resolved = { ...parameters }

    // 等待依赖步骤完成
    for (const depId of dependencies) {
      await this.waitForStepCompletion(depId, context)
    }

    // 解析动态参数
    for (const [key, value] of Object.entries(resolved)) {
      if (typeof value === 'string') {
        resolved[key] = await this.resolveDynamicValue(value, context)
      }
    }

    return resolved
  }

  static async resolveDynamicValue(value, context) {
    // 处理上下文引用：$context.key
    if (value.startsWith('$context.')) {
      const contextKey = value.substring(9)
      const contextValue = context.getContextData(contextKey)
      if (contextValue !== undefined) {
        return contextValue
      }
      throw new Error(`上下文数据不存在: ${contextKey}`)
    }

    // 处理步骤结果引用：$step.stepId.path
    if (value.startsWith('$step.')) {
      const [, stepId, ...pathParts] = value.split('.')
      const stepResult = context.getStepResult(stepId)

      if (!stepResult) {
        throw new Error(`步骤结果不存在: ${stepId}`)
      }

      return this.extractValueByPath(stepResult, pathParts.join('.'))
    }

    // 处理筛选表达式：$filter(stepId.path, condition)
    if (value.startsWith('$filter(')) {
      return this.processFilterExpression(value, context)
    }

    return value
  }

  static extractValueByPath(obj, path) {
    if (!path) return obj

    return path.split('.').reduce((current, key) => {
      // 处理数组索引：projects[0]
      const arrayMatch = key.match(/^(\w+)\[(\d+)\]$/)
      if (arrayMatch) {
        const [, arrayKey, index] = arrayMatch
        return current?.[arrayKey]?.[parseInt(index)]
      }

      // 处理数组筛选：projects[name=okr]
      const filterMatch = key.match(/^(\w+)\[(\w+)=(.+)\]$/)
      if (filterMatch) {
        const [, arrayKey, filterKey, filterValue] = filterMatch
        const array = current?.[arrayKey]
        if (Array.isArray(array)) {
          return array.find((item) => item[filterKey]?.toLowerCase().includes(filterValue.toLowerCase()))
        }
      }

      return current?.[key]
    }, obj)
  }

  static async processFilterExpression(expression, context) {
    // 解析筛选表达式：$filter(step1.projects, name contains "okr")
    const match = expression.match(/\$filter\(([^,]+),\s*(.+)\)/)
    if (!match) {
      throw new Error(`无效的筛选表达式: ${expression}`)
    }

    const [, dataPath, condition] = match
    const data = await this.resolveDynamicValue(`$${dataPath}`, context)

    if (!Array.isArray(data)) {
      throw new Error(`筛选目标必须是数组: ${dataPath}`)
    }

    return this.applyFilter(data, condition)
  }

  static applyFilter(array, condition) {
    // 解析条件：name contains "okr"
    const conditionMatch = condition.match(/(\w+)\s+(contains|equals|startsWith)\s+"([^"]+)"/)
    if (!conditionMatch) {
      throw new Error(`无效的筛选条件: ${condition}`)
    }

    const [, field, operator, value] = conditionMatch

    return array.filter((item) => {
      const fieldValue = item[field]?.toString().toLowerCase() || ''
      const searchValue = value.toLowerCase()

      switch (operator) {
        case 'contains':
          return fieldValue.includes(searchValue)
        case 'equals':
          return fieldValue === searchValue
        case 'startsWith':
          return fieldValue.startsWith(searchValue)
        default:
          return false
      }
    })
  }

  static async waitForStepCompletion(stepId, context) {
    // 等待依赖步骤完成的逻辑
    return new Promise((resolve) => {
      const checkCompletion = () => {
        const stepResult = context.getStepResult(stepId)
        if (stepResult !== undefined) {
          resolve()
        } else {
          setTimeout(checkCompletion, 100)
        }
      }
      checkCompletion()
    })
  }
}

// ExecutionContextManager (保持V1.1的实现，但增强上下文提取)
class ExecutionContextManager {
  constructor(sessionId, userInput) {
    this.sessionId = sessionId
    this.userInput = userInput
    this.stepResults = new Map()
    this.contextData = new Map()
    this.metadata = {
      startTime: Date.now(),
      currentStep: 0,
      totalSteps: 0,
    }
  }

  setStepResult(stepId, result, metadata = {}) {
    this.stepResults.set(stepId, {
      result,
      metadata: {
        ...metadata,
        timestamp: Date.now(),
        stepIndex: this.metadata.currentStep,
      },
    })
    this.extractContextData(stepId, result)
  }

  getStepResult(stepId) {
    return this.stepResults.get(stepId)?.result
  }

  setContextData(key, value) {
    this.contextData.set(key, value)
  }

  getContextData(key) {
    return this.contextData.get(key)
  }

  extractContextData(stepId, result) {
    if (result.data && Array.isArray(result.data)) {
      const targetProject = this.findTargetProject(result.data)
      if (targetProject) {
        this.setContextData('targetProject', targetProject)
      }
    }

    if (result.tasks && Array.isArray(result.tasks)) {
      this.setContextData('taskCount', result.tasks.length)
      this.setContextData(
        'uncompletedTasks',
        result.tasks.filter((t) => !t.completed)
      )
    }
  }

  findTargetProject(projects) {
    const userInput = this.userInput.toLowerCase()
    const keywords = this.extractKeywords(userInput)

    const scored = projects.map((project) => ({
      project,
      score: this.calculateMatchScore(project, keywords),
    }))

    scored.sort((a, b) => b.score - a.score)
    return scored.length > 0 && scored[0].score > 0 ? scored[0].project : null
  }

  extractKeywords(input) {
    const keywords = []
    const projectMatches = input.match(/(\w+)项目|(\w+)project/gi)
    if (projectMatches) {
      projectMatches.forEach((match) => {
        const keyword = match.replace(/项目|project/gi, '')
        if (keyword) keywords.push(keyword.toLowerCase())
      })
    }
    return keywords
  }

  calculateMatchScore(project, keywords) {
    let score = 0
    const projectName = project.name.toLowerCase()
    keywords.forEach((keyword) => {
      if (projectName.includes(keyword)) {
        score += 1
      }
    })
    return score
  }
}

// 简化的 SimpleExecutionPlanner 用于测试
class SimpleExecutionPlanner {
  static async generatePlan(userInput, intentType) {
    const executionPlan = {
      planId: this.generateUUID(),
      userInput: userInput,
      intentType: intentType,
      steps: [],
      totalSteps: 0,
      status: 'pending',
      startTime: Date.now(),
    }

    switch (intentType) {
      case 'find_task':
        if (userInput.includes('项目') || userInput.includes('project')) {
          executionPlan.steps = [
            {
              stepId: this.generateUUID(),
              toolName: 'getProjects',
              description: '获取项目列表',
              parameters: { filter: this.extractProjectKeyword(userInput) },
              dependencies: [],
              status: 'pending',
            },
            {
              stepId: this.generateUUID(),
              toolName: 'getTasks',
              description: '获取项目下的任务',
              parameters: {
                projectId: '$context.targetProject.id',
                completed: false,
              },
              dependencies: ['step1'],
              status: 'pending',
            },
          ]
        } else {
          executionPlan.steps = [
            {
              stepId: this.generateUUID(),
              toolName: 'getTasks',
              description: '获取任务列表',
              parameters: { completed: false },
              dependencies: [],
              status: 'pending',
            },
          ]
        }
        break
      case 'create_task':
        break
      default:
        break
    }

    executionPlan.totalSteps = executionPlan.steps.length
    return executionPlan
  }

  static extractProjectKeyword(input) {
    const matches = input.match(/(\w+)项目|(\w+)project/gi)
    if (matches && matches.length > 0) {
      return matches[0].replace(/项目|project/gi, '')
    }
    return ''
  }

  static generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = (Math.random() * 16) | 0
      const v = c == 'x' ? r : (r & 0x3) | 0x8
      return v.toString(16)
    })
  }
}

// 简化的 simulateToolCall 用于测试
async function simulateToolCall(toolName, parameters) {
  await new Promise((resolve) => setTimeout(resolve, 100)) // 减少延迟用于测试

  switch (toolName) {
    case 'getProjects':
      return {
        success: true,
        data: [
          { id: 'proj-1', name: 'OKR 项目', description: '目标管理项目' },
          { id: 'proj-2', name: '日常任务', description: '日常工作任务' },
        ],
        metadata: { total: 2, filtered: parameters.filter ? 1 : 2 },
      }

    case 'getTasks':
      return {
        success: true,
        tasks: [
          { id: 'task-1', title: '制定 Q1 目标', completed: false, projectId: parameters.projectId || 'proj-1' },
          { id: 'task-2', title: '更新 KR 进度', completed: false, projectId: parameters.projectId || 'proj-1' },
        ],
        metadata: { total: 2, projectId: parameters.projectId },
      }

    default:
      throw new Error(`未知的工具：${toolName}`)
  }
}

// 测试用例 1: 执行上下文管理器测试
async function testExecutionContextManager() {
  console.log('=== 测试执行上下文管理器 ===')

  const context = new ExecutionContextManager('session-1', '查看 okr 项目的任务')

  // 测试项目结果提取
  const projectResult = {
    success: true,
    data: [
      { id: 'proj-1', name: 'OKR 项目', description: '目标管理项目' },
      { id: 'proj-2', name: '日常任务', description: '日常工作任务' },
    ],
  }

  context.setStepResult('step-1', projectResult)

  console.log('上下文数据：', Array.from(context.contextData.keys()))
  console.log('目标项目：', context.getContextData('targetProject'))

  // 测试任务结果提取
  const taskResult = {
    success: true,
    tasks: [
      { id: 'task-1', title: '制定 Q1 目标', completed: false },
      { id: 'task-2', title: '更新 KR 进度', completed: true },
    ],
  }

  context.setStepResult('step-2', taskResult)
  console.log('任务统计：', context.getContextData('taskCount'))
  console.log('未完成任务：', context.getContextData('uncompletedTasks'))
}

// 测试用例 2: 执行计划生成测试
async function testExecutionPlanner() {
  console.log('\n=== 测试执行计划生成器 ===')

  // 测试查找任务计划（包含项目关键词）
  const plan1 = await SimpleExecutionPlanner.generatePlan('查看 okr 项目的任务', 'find_task')
  console.log('项目任务查询计划：')
  console.log('- 计划 ID:', plan1.planId)
  console.log('- 总步骤数：', plan1.totalSteps)
  console.log(
    '- 步骤详情：',
    plan1.steps.map((s) => ({ toolName: s.toolName, description: s.description }))
  )

  // 测试查找任务计划（不包含项目关键词）
  const plan2 = await SimpleExecutionPlanner.generatePlan('查看我的任务', 'find_task')
  console.log('\n直接任务查询计划：')
  console.log('- 计划 ID:', plan2.planId)
  console.log('- 总步骤数：', plan2.totalSteps)
  console.log(
    '- 步骤详情：',
    plan2.steps.map((s) => ({ toolName: s.toolName, description: s.description }))
  )

  // 测试聊天类型（不生成计划）
  const plan3 = await SimpleExecutionPlanner.generatePlan('你好', 'chat')
  console.log('\n聊天类型计划：')
  console.log('- 总步骤数：', plan3.totalSteps)
}

// 测试用例 3: 模拟工具调用测试
async function testSimulateToolCall() {
  console.log('\n=== 测试模拟工具调用 ===')

  try {
    // 测试获取项目
    console.log('测试 getProjects:')
    const projectsResult = await simulateToolCall('getProjects', { filter: 'okr' })
    console.log('- 成功：', projectsResult.success)
    console.log('- 项目数量：', projectsResult.data?.length)
    console.log(
      '- 项目列表：',
      projectsResult.data?.map((p) => p.name)
    )

    // 测试获取任务
    console.log('\n测试 getTasks:')
    const tasksResult = await simulateToolCall('getTasks', { projectId: 'proj-1', completed: false })
    console.log('- 成功：', tasksResult.success)
    console.log('- 任务数量：', tasksResult.tasks?.length)
    console.log(
      '- 任务列表：',
      tasksResult.tasks?.map((t) => t.title)
    )

    // 测试未知工具
    console.log('\n测试未知工具：')
    try {
      await simulateToolCall('unknownTool', {})
    } catch (error) {
      console.log('- 预期错误：', error.message)
    }
  } catch (error) {
    console.error('工具调用测试失败：', error.message)
  }
}

// 测试用例 4: 关键词提取测试
function testKeywordExtraction() {
  console.log('\n=== 测试关键词提取 ===')

  const testCases = ['查看 okr 项目的任务', '显示 project 任务', '我想看看日常项目', '查看所有任务']

  testCases.forEach((input) => {
    const keyword = SimpleExecutionPlanner.extractProjectKeyword(input)
    console.log(`输入："${input}" -> 关键词："${keyword}"`)
  })
}

// 运行所有测试
async function runAllTests() {
  console.log('开始 V1.1 功能测试...\n')

  try {
    await testExecutionContextManager()
    await testExecutionPlanner()
    await testSimulateToolCall()
    testKeywordExtraction()

    console.log('\n✅ 所有测试完成！')
  } catch (error) {
    console.error('\n❌ 测试失败：', error.message)
    console.error(error.stack)
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runAllTests()
}

// V1.2 新增：智能执行计划生成器
class IntelligentExecutionPlanner {
  static async generatePlan(userInput, intentType) {
    const executionPlan = {
      planId: this.generateUUID(),
      userInput: userInput,
      intentType: intentType,
      steps: [],
      totalSteps: 0,
      status: 'pending',
      startTime: Date.now(),
      estimatedTotalTime: 0,
    }

    // 使用AI生成更智能的执行计划
    const analysisPrompt = this.buildAnalysisPrompt(userInput, intentType)

    try {
      const aiResponse = await this.callAI(analysisPrompt)
      const planData = JSON.parse(aiResponse)

      // 构建执行步骤
      for (let i = 0; i < planData.steps.length; i++) {
        const stepData = planData.steps[i]
        const toolConfig = TOOL_REGISTRY[stepData.toolName]

        const step = {
          stepId: this.generateUUID(),
          toolName: stepData.toolName,
          description: stepData.description,
          parameters: stepData.parameters,
          dependencies: stepData.dependencies || [],
          status: 'pending',
          retryCount: 0,
          maxRetries: 3,
          executionTime: null,
          estimatedTime: toolConfig?.metadata?.estimatedTime || 2000,
          error: null,
        }

        executionPlan.steps.push(step)
        executionPlan.estimatedTotalTime += step.estimatedTime
      }

      executionPlan.totalSteps = executionPlan.steps.length
      return executionPlan
    } catch (error) {
      console.warn('AI执行计划解析失败，使用默认计划:', error)
      return this.generateDefaultPlan(userInput, intentType)
    }
  }

  static extractProjectKeyword(input) {
    const matches = input.match(/(\w+)项目|(\w+)project/gi)
    if (matches && matches.length > 0) {
      return matches[0].replace(/项目|project/gi, '')
    }
    return ''
  }

  static buildAnalysisPrompt(userInput, intentType) {
    const toolPrompt = this.generateToolPrompt(TOOL_REGISTRY)

    return `分析用户输入："${userInput}"
意图类型：${intentType}

${toolPrompt}

请生成执行计划，支持以下动态参数引用：
- $context.key: 引用上下文数据
- $step.stepId.path: 引用前置步骤的结果
- $filter(stepId.path, condition): 对数据进行筛选

返回JSON格式：
{
  "analysis": "用户意图分析",
  "steps": [
    {
      "toolName": "工具名称",
      "description": "步骤描述",
      "parameters": {
        "param1": "静态值",
        "param2": "$context.targetProject.id"
      },
      "dependencies": ["step1"],
      "reasoning": "选择此工具的原因"
    }
  ]
}`
  }

  static generateToolPrompt(toolRegistry) {
    let prompt = '可用工具列表：\n'
    for (const [toolName, config] of Object.entries(toolRegistry)) {
      prompt += `- ${toolName}: ${config.description}\n`
      prompt += `  参数: ${JSON.stringify(config.parameters)}\n`
    }
    return prompt
  }

  static async callAI(prompt) {
    // 调用豆包AI生成执行计划
    const openai = new OpenAI(doubaoParams)
    const response = await openai.chat.completions.create({
      messages: [
        { role: 'system', content: '你是一个专业的任务执行计划生成器。' },
        { role: 'user', content: prompt },
      ],
      model: 'doubao-seed-1-6-250615',
      stream: false,
      timeout: 30000,
    })

    return response.choices[0].message.content
  }

  static generateDefaultPlan(userInput, intentType) {
    // 当AI生成失败时的默认计划
    const executionPlan = {
      planId: this.generateUUID(),
      userInput: userInput,
      intentType: intentType,
      steps: [],
      totalSteps: 0,
      status: 'pending',
      startTime: Date.now(),
      estimatedTotalTime: 0,
    }

    // 基于规则的默认计划生成
    if (intentType === 'find_task') {
      if (userInput.includes('项目') || userInput.includes('project')) {
        const step1Id = this.generateUUID()
        executionPlan.steps = [
          {
            stepId: step1Id,
            toolName: 'getProjects',
            description: '获取项目列表',
            parameters: { filter: this.extractProjectKeyword(userInput) },
            dependencies: [],
            status: 'pending',
            estimatedTime: 1500,
          },
          {
            stepId: this.generateUUID(),
            toolName: 'getTasks',
            description: '获取项目下的任务',
            parameters: {
              projectId: '$context.targetProject.id',
              completed: false,
            },
            dependencies: [step1Id],
            status: 'pending',
            estimatedTime: 2000,
          },
        ]
        executionPlan.estimatedTotalTime = 3500
      }
    }

    executionPlan.totalSteps = executionPlan.steps.length
    return executionPlan
  }

  static generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = (Math.random() * 16) | 0
      const v = c == 'x' ? r : (r & 0x3) | 0x8
      return v.toString(16)
    })
  }
}

// V1.1 基础工具调用引擎
async function executeSimplePlan(executionPlan, context, sseChannel) {
  try {
    // 推送执行计划开始
    await sseChannel.write({
      type: 'execution_plan_start',
      plan: {
        planId: executionPlan.planId,
        totalSteps: executionPlan.totalSteps,
      },
      timestamp: Date.now(),
    })

    executionPlan.status = 'executing'

    for (let i = 0; i < executionPlan.steps.length; i++) {
      const step = executionPlan.steps[i]
      context.metadata.currentStep = i

      // 推送当前执行步骤
      await sseChannel.write({
        type: 'execution_step',
        step: {
          stepId: step.stepId,
          description: step.description,
          toolName: step.toolName,
        },
        timestamp: Date.now(),
      })

      step.status = 'executing'

      try {
        // V1.1 版本：简单的工具调用（不支持动态参数）
        const validatedParams = ParameterValidator.validate(step.toolName, step.parameters)

        // 模拟工具调用（V1.1 版本先返回模拟数据）
        const result = await simulateToolCall(step.toolName, validatedParams)

        // 存储结果到上下文
        context.setStepResult(step.stepId, result)

        step.status = 'completed'

        // 推送步骤执行结果
        await sseChannel.write({
          type: 'step_result',
          stepId: step.stepId,
          result: result,
          timestamp: Date.now(),
        })
      } catch (error) {
        step.status = 'failed'
        step.error = error.message

        await sseChannel.write({
          type: 'step_error',
          stepId: step.stepId,
          error: error.message,
          timestamp: Date.now(),
        })

        throw error
      }
    }

    executionPlan.status = 'completed'

    // 推送执行完成
    await sseChannel.write({
      type: 'execution_complete',
      plan: executionPlan,
      contextData: Array.from(context.contextData.keys()),
      timestamp: Date.now(),
    })

    return executionPlan
  } catch (error) {
    executionPlan.status = 'failed'
    executionPlan.error = error.message

    await sseChannel.write({
      type: 'execution_failed',
      plan: executionPlan,
      error: error.message,
      timestamp: Date.now(),
    })

    throw error
  }
}

// V1.2 新增：真实工具调用函数
async function callRealTool(toolName, parameters) {
  const toolConfig = TOOL_REGISTRY[toolName]
  if (!toolConfig) {
    throw new Error(`未找到工具：${toolName}`)
  }

  try {
    // 调用对应的云函数
    const cloudFunction = uniCloud.importObject(toolConfig.cloudFunction)
    const result = await cloudFunction[toolConfig.method](parameters)

    return result
  } catch (error) {
    throw new Error(`工具执行失败：${error.message}`)
  }
}

// V1.2 新增：获取上下文更新信息
function getContextUpdates(context) {
  const updates = {}
  for (const [key, value] of context.contextData.entries()) {
    updates[key] = value
  }
  return updates
}

// V1.2 新增：生成执行摘要
function generateExecutionSummary(executionPlan, context) {
  const completedSteps = executionPlan.steps.filter((s) => s.status === 'completed')
  const failedSteps = executionPlan.steps.filter((s) => s.status === 'failed')

  return {
    totalSteps: executionPlan.totalSteps,
    completedSteps: completedSteps.length,
    failedSteps: failedSteps.length,
    totalExecutionTime: executionPlan.totalExecutionTime,
    averageStepTime:
      completedSteps.length > 0
        ? Math.round(completedSteps.reduce((sum, s) => sum + s.executionTime, 0) / completedSteps.length)
        : 0,
    contextDataKeys: Array.from(context.contextData.keys()),
    success: failedSteps.length === 0,
  }
}

// V1.2 新增：智能执行引擎
async function executeIntelligentPlan(executionPlan, context, sseChannel) {
  try {
    // 推送执行计划
    await sseChannel.write({
      type: 'execution_plan',
      plan: {
        planId: executionPlan.planId,
        totalSteps: executionPlan.totalSteps,
        estimatedTotalTime: executionPlan.estimatedTotalTime,
      },
      timestamp: Date.now(),
    })

    executionPlan.status = 'executing'

    for (let i = 0; i < executionPlan.steps.length; i++) {
      const step = executionPlan.steps[i]
      context.metadata.currentStep = i

      // 推送当前执行步骤
      await sseChannel.write({
        type: 'execution_step',
        step: {
          stepId: step.stepId,
          description: step.description,
          toolName: step.toolName,
          estimatedTime: step.estimatedTime,
        },
        timestamp: Date.now(),
      })

      step.status = 'executing'
      const stepStartTime = Date.now()

      try {
        // V1.2核心：动态参数解析
        const resolvedParams = await DynamicParameterResolver.resolveParameters(step, context)

        // 参数验证
        const validatedParams = ParameterValidator.validate(step.toolName, resolvedParams)

        // 真实工具调用
        const result = await callRealTool(step.toolName, validatedParams)

        // 存储结果到上下文
        context.setStepResult(step.stepId, result)

        step.status = 'completed'
        step.executionTime = Date.now() - stepStartTime

        // 推送步骤执行结果
        await sseChannel.write({
          type: 'step_result',
          stepId: step.stepId,
          result: result,
          executionTime: step.executionTime,
          contextUpdates: getContextUpdates(context),
          timestamp: Date.now(),
        })
      } catch (error) {
        step.executionTime = Date.now() - stepStartTime
        step.status = 'failed'
        step.error = error.message

        await sseChannel.write({
          type: 'step_error',
          stepId: step.stepId,
          error: error.message,
          executionTime: step.executionTime,
          timestamp: Date.now(),
        })

        // V1.2版本：简单的错误处理，V1.3版本会完善
        throw error
      }
    }

    executionPlan.status = 'completed'
    executionPlan.endTime = Date.now()
    executionPlan.totalExecutionTime = executionPlan.endTime - executionPlan.startTime

    // 推送执行完成
    await sseChannel.write({
      type: 'execution_complete',
      plan: executionPlan,
      summary: generateExecutionSummary(executionPlan, context),
      timestamp: Date.now(),
    })

    return executionPlan
  } catch (error) {
    executionPlan.status = 'failed'
    executionPlan.error = error.message

    await sseChannel.write({
      type: 'execution_failed',
      plan: executionPlan,
      error: error.message,
      timestamp: Date.now(),
    })

    throw error
  }
}

// 模拟工具调用（V1.1版本使用，V1.2版本替换为真实调用）
async function simulateToolCall(toolName, parameters) {
  // 模拟延迟
  await new Promise((resolve) => setTimeout(resolve, 1000))

  switch (toolName) {
    case 'getProjects':
      return {
        success: true,
        data: [
          { id: 'proj-1', name: 'OKR 项目', description: '目标管理项目' },
          { id: 'proj-2', name: '日常任务', description: '日常工作任务' },
        ],
        metadata: { total: 2, filtered: parameters.filter ? 1 : 2 },
      }

    case 'getTasks':
      return {
        success: true,
        tasks: [
          { id: 'task-1', title: '制定 Q1 目标', completed: false, projectId: parameters.projectId || 'proj-1' },
          { id: 'task-2', title: '更新 KR 进度', completed: false, projectId: parameters.projectId || 'proj-1' },
        ],
        metadata: { total: 2, projectId: parameters.projectId },
      }

    default:
      throw new Error(`未知的工具：${toolName}`)
  }
}

// V1.1 版本新增的 SSE 消息类型
const SSE_MESSAGE_TYPES = {
  // 现有类型保持不变
  start: '开始生成回复',
  intent_type: '意图类型识别',
  intent_content_start: '意图内容开始',
  intent_content_chunk: '意图内容块',
  end: '结束',
  error: '错误',

  // V1.1 新增类型
  execution_plan_start: '执行计划开始',
  execution_step: '执行步骤',
  step_result: '步骤结果',
  step_error: '步骤错误',
  execution_complete: '执行完成',
  execution_failed: '执行失败',
}

module.exports = {
  _before: function () {
    // 通用预处理器
  },

  /**
   * 与 DeepSeek AI 进行聊天对话
   * @param {string} message - 用户当前发送的消息内容
   * @param {Array} messages - 历史对话消息数组，每条消息包含 role 和 content
   * @param {string} model - AI 模型名称，默认为'deepseek-chat'
   * @param {string} system - 系统提示词，用于设定 AI 助手的行为和角色
   * @returns {object} AI 回复内容或错误信息
   */
  async speak() {
    // 获取 HTTP 请求信息
    const httpInfo = this.getHttpInfo()
    // 解析请求体，提取参数
    let {
      message,
      history_records = [], // 默认为空数组
      model = 'deepseek-chat', // 默认使用 deepseek-chat 模型
      system = 'You are a helpful assistant.', // 默认系统提示词
    } = JSON.parse(httpInfo.body)

    // 参数校验：确保 message 不为空
    if (!message) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: '消息内容不能为空',
      }
    }

    try {
      // 初始化 OpenAI 客户端，配置为 DeepSeek API
      const openai = new OpenAI({
        baseURL: 'https://api.deepseek.com/v1', // DeepSeek API 地址
        apiKey: '***********************************', // API 密钥
      })

      // 调用聊天完成接口
      const messages = [{ role: 'system', content: system }, ...history_records]
      if (message) messages.push({ role: 'user', content: message })
      const completion = await openai.chat.completions.create({
        // 构建消息数组：系统提示词 + 历史消息 + 当前用户消息
        messages,
        model: model, // 使用指定的模型
      })

      // 返回 AI 回复的内容
      return {
        content: completion.choices[0].message.content,
      }
    } catch (error) {
      // 错误处理：返回错误代码和消息
      return {
        errCode: 'API_ERROR',
        errMsg: error.message || '调用 AI 接口失败',
      }
    }
  },
  // async chat(params) {
  //   let {
  //     message,
  //     messages: history_records = [], // 默认为空数组
  //     model = 'doubao-seed-1-6-250615', // 默认使用 doubao 模型
  //     system = 'You are a helpful assistant.', // 默认系统提示词
  //   } = params

  //   console.log('message', message)

  //   // 参数校验：确保 message 不为空
  //   if (!message) {
  //     return {
  //       errCode: 'PARAM_IS_NULL',
  //       errMsg: '消息内容不能为空',
  //     }
  //   }
  //   console.log('发送消息')

  //   try {
  // console.time('初始化')
  //     // 初始化 OpenAI 客户端
  //     const openai = new OpenAI(doubaoParams)
  // console.timeEnd('初始化')
  //     // 调用聊天完成接口
  //     const messages = [{ role: 'system', content: system }, ...history_records]
  //     if (message) messages.push({ role: 'user', content: message })
  // console.time('获取响应流')
  //     // 获取响应流
  //     const streamResponse = await openai.chat.completions.create({
  //       messages,
  //       model: model,
  //       stream: true, // 始终开启流式响应
  //     })
  //  console.timeEnd('获取响应流')

  //     // 处理流式响应，收集所有数据块
  //     let fullContent = ''
  //     const chunks = []

  //     for await (const chunk of streamResponse) {
  //       const content = chunk.choices[0]?.delta?.content || ''
  //       console.log(content)
  //       if (content) {
  //         fullContent += content
  //         chunks.push({
  //           content: content,
  //           timestamp: Date.now(),
  //         })
  //       }
  //     }

  //     // 返回流式响应的完整结果
  //     return {
  //       errCode: 0,
  //       errMsg: 'success',
  //       data: {
  //         type: 'stream',
  //         content: fullContent, // 完整内容
  //         chunks: chunks, // 分块数据，可用于前端逐步显示
  //       },
  //     }
  //   } catch (error) {
  //     // 错误处理：返回标准失败响应结构
  //     console.log(error)
  //     return {
  //       errCode: 'API_ERROR',
  //       errMsg: error.message || '调用 AI 接口失败',
  //     }
  //   }
  // },

  /**
   * 基于 SSE Channel 的流式聊天接口 - 实时推送 AI 响应
   * @param {object} params - 参数对象
   * @returns {object} 流式响应结果
   */
  async chatStreamSSE(params) {
    let {
      message,
      messages: history_records = [],
      model = 'doubao-seed-1-6-250615',
      system = `你是一个专业的任务分析助手。你的主要职责是理解用户输入的内容，并判断用户的意图类型。

请分析用户输入内容，并将其归类为以下三种意图之一：
1. create_task: 当用户想要创建、添加、设置、安排一个新任务时
2. find_task: 当用户想要查询、搜索、查看已有的任务时
3. chat: 其他所有不属于创建任务或查找任务的内容，视为一般闲聊

分析完成后，必须严格按照以下格式输出结果：
「意图类型」：[意图类型代码，必须是 create_task、find_task 或 chat 之一]
「意图内容」：[如果是创建任务，提取出要创建的任务内容；如果是查找任务，提取出要查找的任务关键词；如果是闲聊，则回复用户问题]

注意：
- 分析要准确，不要混淆不同意图类型
- 只输出上述指定的两行内容，不要添加任何其他解释或内容
- 确保格式严格遵循示例，包括使用中文引号「」`,
      channel, // SSE Channel 对象
    } = params

    console.log('SSE 流式聊天消息：', message)

    // 参数校验
    if (!message) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: '消息内容不能为空',
      }
    }

    if (!channel) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: 'SSE Channel 不能为空',
      }
    }

    try {
      // 反序列化 SSE Channel
      const sseChannel = uniCloud.deserializeSSEChannel(channel)

      // 初始化 OpenAI 客户端
      const openai = new OpenAI(doubaoParams)
      // 构建消息数组
      const messages = [{ role: 'system', content: system }, ...history_records]
      if (message) messages.push({ role: 'user', content: message })

      // 发送开始消息
      await sseChannel.write({
        type: 'start',
        message: '开始生成回复...',
        timestamp: Date.now(),
      })

      // 创建流式响应，设置 5 分钟超时
      const streamResponse = await openai.chat.completions.create({
        messages,
        model: model,
        stream: true,
        timeout: 300000, // 5 分钟超时（毫秒）
      })

      // 处理流式数据并实时推送
      let fullContent = ''
      let chunkCount = 0

      // 意图识别相关变量
      let intentType = null
      let isIntentContentStarted = false
      let intentContent = ''

      // 意图类型和内容的正则表达式
      const intentTypeRegex = /「意图类型」：(create_task|find_task|chat)/
      const intentContentRegex = /「意图内容」：([\s\S]*)/

      for await (const chunk of streamResponse) {
        const content = chunk.choices[0]?.delta?.content || ''
        if (content) {
          fullContent += content
          chunkCount++

          // 检测意图类型
          if (!intentType) {
            const typeMatch = intentTypeRegex.exec(fullContent)
            if (typeMatch) {
              intentType = typeMatch[1]
              console.log(`检测到意图类型：${intentType}`)
              // 推送意图类型
              await sseChannel.write({
                type: 'intent_type',
                intentType: intentType,
                timestamp: Date.now(),
              })
              continue // 跳过当前块的推送
            }
          }

          // 检测意图内容
          if (intentType && !isIntentContentStarted) {
            const contentMatch = intentContentRegex.exec(fullContent)
            if (contentMatch) {
              isIntentContentStarted = true
              intentContent = contentMatch[1]
              console.log('检测到意图内容开始')
              // 推送意图内容开始
              await sseChannel.write({
                type: 'intent_content_start',
                content: intentContent,
                timestamp: Date.now(),
              })
              continue // 跳过当前块的推送
            }
          } else if (isIntentContentStarted) {
            // 继续推送意图内容的后续部分
            await sseChannel.write({
              type: 'intent_content_chunk',
              content: content,
              timestamp: Date.now(),
            })
            intentContent += content
            console.log(`推送意图内容块：${content}`)
          } else {
            // 尚未检测到任何模式，继续累积内容
            console.log(`累积内容：${fullContent}`)
          }
        }
      }

      // 打印完整的 AI 返回内容，用于调试
      console.log('AI 完整返回内容：', fullContent)
      console.log('提取的意图类型：', intentType)
      console.log('提取的意图内容：', intentContent)

      // V1.2 升级：在意图识别完成后执行任务
      if (intentType && intentType !== 'chat') {
        // 创建执行上下文
        const context = new ExecutionContextManager(IntelligentExecutionPlanner.generateUUID(), message)

        // 使用智能执行计划生成器
        const executionPlan = await IntelligentExecutionPlanner.generatePlan(message, intentType)

        if (executionPlan.totalSteps > 0) {
          // 使用智能执行引擎
          await executeIntelligentPlan(executionPlan, context, sseChannel)

          // 修改最终返回结果
          return {
            errCode: 0,
            errMsg: 'success',
            data: {
              type: 'task_executed',
              intentType: intentType,
              executionPlan: executionPlan,
              contextData: Array.from(context.contextData.keys()),
              executionTime: executionPlan.totalExecutionTime,
              content: isIntentContentStarted ? intentContent : fullContent,
              totalChunks: chunkCount,
            },
          }
        }
      }

      // 发送结束消息
      await sseChannel.end({
        type: 'end',
        content: isIntentContentStarted ? intentContent : fullContent,
        intentType: intentType,
        totalChunks: chunkCount,
        timestamp: Date.now(),
      })

      console.log(`SSE 流式聊天完成，共推送${chunkCount}个数据块`)

      return {
        errCode: 0,
        errMsg: 'success',
        data: {
          type: 'stream_complete',
          content: isIntentContentStarted ? intentContent : fullContent,
          intentType: intentType,
          totalChunks: chunkCount,
        },
      }
    } catch (error) {
      console.log('SSE 流式聊天错误：', error)

      // 如果有 channel，尝试发送错误消息
      try {
        if (channel) {
          const sseChannel = uniCloud.deserializeSSEChannel(channel)
          await sseChannel.end({
            type: 'error',
            error: error.message || '调用 AI 流式接口失败',
            timestamp: Date.now(),
          })
        }
      } catch (channelError) {
        console.log('发送错误消息失败：', channelError)
      }

      return {
        errCode: 'API_ERROR',
        errMsg: error.message || '调用 AI 流式接口失败',
      }
    }
  },
}
