// 云对象教程：https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
// jsdoc 语法提示教程：https://ask.dcloud.net.cn/docs/#//ask.dcloud.net.cn/article/129

// 不再需要 axios 依赖
// const axios = require('axios')

// 滴答清单官方 API 基础 URL
const BASE_URL = 'https://api.dida365.com/open/v1'

// 使用预设 token，不需要客户端提供
const token = '94153c68-343d-49b8-8f4c-c9504cf86a22'

module.exports = {
  _before: function () {
    // 通用预处理器
  },

  /**
   * 获取任务详情
   * @param {object} params 参数对象
   * @param {string} params.projectId 项目 ID，必需
   * @param {string} params.taskId 任务 ID，必需
   * @returns {object} 任务详情
   */
  getTask: async function (params) {
    console.log('--- 调用 getTask ---', params)
    const { projectId, taskId } = params || {}

    if (!projectId || !taskId) {
      return {
        errCode: 'INVALID_PARAMS',
        errMsg: '项目 ID 和任务 ID 都是必需的参数',
      }
    }

    try {
      const url = `${BASE_URL}/project/${projectId}/task/${taskId}`
      console.log('请求 URL:', url)

      const response = await uniCloud.httpclient.request(url, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        dataType: 'json',
      })

      console.log('getTask 成功，响应数据：', response.data)

      return {
        success: true,
        data: response.data,
      }
    } catch (error) {
      console.error('getTask 失败：', error)
      return {
        errCode: 'GET_TASK_FAILED',
        errMsg: error.message || '获取任务详情失败',
        error: error.response ? error.response.data : null,
      }
    }
  },

  /**
   * 获取任务列表（通过项目获取）
   * @param {object} params 参数对象
   * @param {string} [params.projectId] 项目 ID，可选。如果不提供则获取所有项目的任务
   * @param {boolean} [params.completed=false] 是否获取已完成的任务，可选，默认为 false
   * @param {number} [params.limit=50] 返回任务数量限制，可选，默认为 50，最大 100
   * @returns {object} 任务列表
   */
  getTasks: async function (params) {
    console.log('--- 调用 getTasks ---', params)
    const { projectId, completed = false, limit = 50 } = params || {}

    try {
      let allTasks = []

      if (projectId) {
        // 使用项目数据接口获取项目的任务
        const url = `${BASE_URL}/project/${projectId}/data`
        console.log('请求 URL:', url)

        const response = await uniCloud.httpclient.request(url, {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          dataType: 'json',
        })

        allTasks = response.data?.tasks || []
      } else {
        // 如果没有指定项目 ID，先获取所有项目，然后获取每个项目的任务
        const projectsResult = await this.getProjects()
        if (!projectsResult.success) {
          return projectsResult
        }

        const projects = projectsResult.data || []

        // 并发获取所有项目的任务
        const taskPromises = projects.map(async (project) => {
          try {
            const url = `${BASE_URL}/project/${project.id}/data`
            const response = await uniCloud.httpclient.request(url, {
              method: 'GET',
              headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
              },
              dataType: 'json',
            })
            return response.data?.tasks || []
          } catch (error) {
            console.error(`获取项目 ${project.id} 的任务失败:`, error)
            return []
          }
        })

        const taskArrays = await Promise.all(taskPromises)
        allTasks = taskArrays.flat()
      }

      // 根据完成状态过滤任务
      let filteredTasks = allTasks
      if (completed !== undefined) {
        filteredTasks = allTasks.filter((task) => {
          // status: 0 表示未完成，2 表示已完成
          const isCompleted = task.status === 2
          return completed ? isCompleted : !isCompleted
        })
      }

      // 限制返回数量
      const limitedTasks = filteredTasks.slice(0, Math.min(limit, 100))

      console.log('getTasks 成功，响应数据：', limitedTasks)

      return {
        success: true,
        data: limitedTasks,
        total: filteredTasks.length,
      }
    } catch (error) {
      console.error('getTasks 失败：', error)
      return {
        errCode: 'GET_TASKS_FAILED',
        errMsg: error.message || '获取任务列表失败',
        error: error.response ? error.response.data : null,
      }
    }
  },

  /**
   * 创建任务
   * @param {object} params 参数对象
   * @param {object} params.taskData 任务数据
   * @returns {object} 创建结果
   */
  createTask: async function (params) {
    console.log('--- 调用 createTask ---', params)
    const { taskData } = params || {}

    if (!taskData) {
      return {
        errCode: 'PARAMS_REQUIRED',
        errMsg: '需要提供任务数据',
      }
    }

    // 至少需要标题
    if (!taskData.title) {
      return {
        errCode: 'TITLE_REQUIRED',
        errMsg: '任务标题不能为空',
      }
    }

    try {
      const response = await uniCloud.httpclient.request(`${BASE_URL}/task`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        data: taskData,
        contentType: 'json',
        dataType: 'json',
      })

      console.log('createTask 成功，响应数据：', response.data)

      return {
        success: true,
        data: response.data,
      }
    } catch (error) {
      console.error('createTask 失败：', error)
      return {
        errCode: 'CREATE_TASK_FAILED',
        errMsg: error.message || '创建任务失败',
        error: error.response ? error.response.data : null,
      }
    }
  },

  /**
   * 更新任务
   * @param {object} params 参数对象
   * @param {string} params.taskId 任务 ID
   * @param {object} params.taskData 任务更新数据（必须包含 id 和 projectId）
   * @returns {object} 更新结果
   */
  updateTask: async function (params) {
    console.log('--- 调用 updateTask ---', params)
    const { taskId, taskData } = params || {}

    if (!taskId || !taskData) {
      return {
        errCode: 'PARAMS_REQUIRED',
        errMsg: '需要提供任务 ID 和更新数据',
      }
    }

    // 检查必需的字段
    if (!taskData.projectId) {
      return {
        errCode: 'PROJECT_ID_REQUIRED',
        errMsg: '更新数据中必须包含 projectId',
      }
    }

    try {
      // 确保任务 ID 包含在数据中
      taskData.id = taskId

      const response = await uniCloud.httpclient.request(`${BASE_URL}/task/${taskId}`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        data: taskData,
        contentType: 'json',
        dataType: 'json',
      })

      console.log('updateTask 成功，响应数据：', response.data)

      return {
        success: true,
        data: response.data,
      }
    } catch (error) {
      console.error('updateTask 失败：', error)
      return {
        errCode: 'UPDATE_TASK_FAILED',
        errMsg: error.message || '更新任务失败',
        error: error.response ? error.response.data : null,
      }
    }
  },

  /**
   * 删除任务
   * @param {object} params 参数对象
   * @param {string} params.projectId 项目 ID，必需
   * @param {string} params.taskId 任务 ID，必需
   * @returns {object} 删除结果
   */
  deleteTask: async function (params) {
    console.log('--- 调用 deleteTask ---', params)
    const { projectId, taskId } = params || {}

    if (!projectId || !taskId) {
      return {
        errCode: 'PARAMS_REQUIRED',
        errMsg: '需要提供项目 ID 和任务 ID',
      }
    }

    try {
      const response = await uniCloud.httpclient.request(`${BASE_URL}/project/${projectId}/task/${taskId}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        dataType: 'json',
      })

      console.log('deleteTask 成功，响应数据：', response.data)

      return {
        success: true,
        data: response.data,
      }
    } catch (error) {
      console.error('deleteTask 失败：', error)
      return {
        errCode: 'DELETE_TASK_FAILED',
        errMsg: error.message || '删除任务失败',
        error: error.response ? error.response.data : null,
      }
    }
  },

  /**
   * 完成任务
   * @param {object} params 参数对象
   * @param {string} params.projectId 项目 ID，必需
   * @param {string} params.taskId 任务 ID，必需
   * @returns {object} 操作结果
   */
  completeTask: async function (params) {
    console.log('--- 调用 completeTask ---', params)
    const { projectId, taskId } = params || {}

    if (!projectId || !taskId) {
      return {
        errCode: 'PARAMS_REQUIRED',
        errMsg: '需要提供项目 ID 和任务 ID',
      }
    }

    try {
      const response = await uniCloud.httpclient.request(`${BASE_URL}/project/${projectId}/task/${taskId}/complete`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        data: {},
        contentType: 'json',
        dataType: 'json',
      })

      console.log('completeTask 成功，响应数据：', response.data)

      return {
        success: true,
        data: response.data,
      }
    } catch (error) {
      console.error('completeTask 失败：', error)
      return {
        errCode: 'COMPLETE_TASK_FAILED',
        errMsg: error.message || '完成任务失败',
        error: error.response ? error.response.data : null,
      }
    }
  },

  /**
   * 获取项目详情
   * @param {object} params 参数对象
   * @param {string} params.projectId 项目 ID，必需
   * @returns {object} 项目详情
   */
  getProject: async function (params) {
    console.log('--- 调用 getProject ---', params)
    const { projectId } = params || {}

    if (!projectId) {
      return {
        errCode: 'PARAMS_REQUIRED',
        errMsg: '需要提供项目 ID',
      }
    }

    try {
      const response = await uniCloud.httpclient.request(`${BASE_URL}/project/${projectId}`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        dataType: 'json',
      })

      console.log('getProject 成功，响应数据：', response.data)

      return {
        success: true,
        data: response.data,
      }
    } catch (error) {
      console.error('getProject 失败：', error)
      return {
        errCode: 'GET_PROJECT_FAILED',
        errMsg: error.message || '获取项目详情失败',
        error: error.response ? error.response.data : null,
      }
    }
  },

  /**
   * 获取项目及其数据
   * @param {object} params 参数对象
   * @param {string} params.projectId 项目 ID，必需
   * @returns {object} 项目数据（包含项目信息、任务列表、列信息）
   */
  getProjectData: async function (params) {
    console.log('--- 调用 getProjectData ---', params)
    const { projectId } = params || {}

    if (!projectId) {
      return {
        errCode: 'PARAMS_REQUIRED',
        errMsg: '需要提供项目 ID',
      }
    }

    try {
      const response = await uniCloud.httpclient.request(`${BASE_URL}/project/${projectId}/data`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        dataType: 'json',
      })

      console.log('getProjectData 成功，响应数据：', response.data)

      return {
        success: true,
        data: response.data,
      }
    } catch (error) {
      console.error('getProjectData 失败：', error)
      return {
        errCode: 'GET_PROJECT_DATA_FAILED',
        errMsg: error.message || '获取项目数据失败',
        error: error.response ? error.response.data : null,
      }
    }
  },

  /**
   * 获取项目列表
   * @returns {object} 项目列表
   */
  getProjects: async function () {
    console.log('--- 调用 getProjects ---')
    try {
      const response = await uniCloud.httpclient.request(`${BASE_URL}/project`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        dataType: 'json',
      })

      console.log('getProjects 成功，响应数据：', response.data)

      return {
        success: true,
        data: response.data,
      }
    } catch (error) {
      console.error('getProjects 失败：', error)
      return {
        errCode: 'GET_PROJECTS_FAILED',
        errMsg: error.message || '获取项目列表失败',
        error: error.response ? error.response.data : null,
      }
    }
  },

  /**
   * 创建项目
   * @param {object} params 参数对象
   * @param {object} params.projectData 项目数据
   * @param {string} params.projectData.name 项目名称，必需
   * @param {string} [params.projectData.color] 项目颜色
   * @param {number} [params.projectData.sortOrder] 排序值
   * @param {string} [params.projectData.viewMode] 视图模式："list", "kanban", "timeline"
   * @param {string} [params.projectData.kind] 项目类型："TASK", "NOTE"
   * @returns {object} 创建结果
   */
  createProject: async function (params) {
    console.log('--- 调用 createProject ---', params)
    const { projectData } = params || {}

    if (!projectData || !projectData.name) {
      return {
        errCode: 'PARAMS_REQUIRED',
        errMsg: '需要提供项目数据，且项目名称不能为空',
      }
    }

    try {
      const response = await uniCloud.httpclient.request(`${BASE_URL}/project`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        data: projectData,
        contentType: 'json',
        dataType: 'json',
      })

      console.log('createProject 成功，响应数据：', response.data)

      return {
        success: true,
        data: response.data,
      }
    } catch (error) {
      console.error('createProject 失败：', error)
      return {
        errCode: 'CREATE_PROJECT_FAILED',
        errMsg: error.message || '创建项目失败',
        error: error.response ? error.response.data : null,
      }
    }
  },

  /**
   * 更新项目
   * @param {object} params 参数对象
   * @param {string} params.projectId 项目 ID，必需
   * @param {object} params.projectData 项目更新数据
   * @returns {object} 更新结果
   */
  updateProject: async function (params) {
    console.log('--- 调用 updateProject ---', params)
    const { projectId, projectData } = params || {}

    if (!projectId || !projectData) {
      return {
        errCode: 'PARAMS_REQUIRED',
        errMsg: '需要提供项目 ID 和更新数据',
      }
    }

    try {
      const response = await uniCloud.httpclient.request(`${BASE_URL}/project/${projectId}`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        data: projectData,
        contentType: 'json',
        dataType: 'json',
      })

      console.log('updateProject 成功，响应数据：', response.data)

      return {
        success: true,
        data: response.data,
      }
    } catch (error) {
      console.error('updateProject 失败：', error)
      return {
        errCode: 'UPDATE_PROJECT_FAILED',
        errMsg: error.message || '更新项目失败',
        error: error.response ? error.response.data : null,
      }
    }
  },

  /**
   * 删除项目
   * @param {object} params 参数对象
   * @param {string} params.projectId 项目 ID，必需
   * @returns {object} 删除结果
   */
  deleteProject: async function (params) {
    console.log('--- 调用 deleteProject ---', params)
    const { projectId } = params || {}

    if (!projectId) {
      return {
        errCode: 'PARAMS_REQUIRED',
        errMsg: '需要提供项目 ID',
      }
    }

    try {
      const response = await uniCloud.httpclient.request(`${BASE_URL}/project/${projectId}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        dataType: 'json',
      })

      console.log('deleteProject 成功，响应数据：', response.data)

      return {
        success: true,
        data: response.data,
      }
    } catch (error) {
      console.error('deleteProject 失败：', error)
      return {
        errCode: 'DELETE_PROJECT_FAILED',
        errMsg: error.message || '删除项目失败',
        error: error.response ? error.response.data : null,
      }
    }
  },
}
