// V1.2 动态参数解析功能测试脚本
// 测试动态参数解析器、智能执行计划生成器和真实工具调用引擎

const { 
  DynamicParameterResolver, 
  IntelligentExecutionPlanner, 
  ExecutionContextManager,
  ParameterValidator,
  TOOL_REGISTRY 
} = require('./index.obj.js')

// 测试用例 1: 动态参数解析测试
async function testDynamicParameterResolver() {
  console.log('=== 测试动态参数解析器 ===')
  
  const context = new ExecutionContextManager('session-1', '查看okr项目任务')
  
  // 设置上下文数据
  context.setContextData('targetProject', { id: 'proj-1', name: 'OKR项目' })
  
  // 设置步骤结果
  context.setStepResult('step1', {
    success: true,
    data: [
      { id: 'proj-1', name: 'OKR项目' },
      { id: 'proj-2', name: '日常任务' }
    ]
  })
  
  // 测试上下文引用
  const step1 = {
    parameters: { 
      projectId: '$context.targetProject.id', 
      completed: false 
    },
    dependencies: []
  }
  
  const resolved1 = await DynamicParameterResolver.resolveParameters(step1, context)
  console.log('上下文引用解析结果:', resolved1)
  console.log('期望 projectId 为 "proj-1":', resolved1.projectId === 'proj-1')
  
  // 测试步骤结果引用
  const step2 = {
    parameters: { 
      projectName: '$step.step1.data[0].name'
    },
    dependencies: ['step1']
  }
  
  const resolved2 = await DynamicParameterResolver.resolveParameters(step2, context)
  console.log('步骤结果引用解析结果:', resolved2)
  console.log('期望 projectName 为 "OKR项目":', resolved2.projectName === 'OKR项目')
}

// 测试用例 2: 筛选表达式测试
async function testFilterExpression() {
  console.log('\n=== 测试筛选表达式处理 ===')
  
  const context = new ExecutionContextManager('session-1', 'test')
  context.setStepResult('step1', {
    success: true,
    data: [
      { id: 'proj-1', name: 'OKR项目' },
      { id: 'proj-2', name: '日常任务' },
      { id: 'proj-3', name: 'OKR管理' }
    ]
  })
  
  try {
    const result = await DynamicParameterResolver.processFilterExpression(
      '$filter(step1.data, name contains "okr")', 
      context
    )
    
    console.log('筛选结果:', result)
    console.log('期望筛选出2个项目:', result.length === 2)
    console.log('第一个项目名称包含OKR:', result[0].name.toLowerCase().includes('okr'))
  } catch (error) {
    console.error('筛选表达式测试失败:', error.message)
  }
}

// 测试用例 3: 参数验证器测试
function testParameterValidator() {
  console.log('\n=== 测试参数验证器 ===')
  
  try {
    // 测试正常参数验证
    const validParams = ParameterValidator.validate('getTasks', {
      projectId: 'proj-1',
      completed: false,
      limit: '10'
    })
    
    console.log('参数验证结果:', validParams)
    console.log('limit 类型转换正确:', typeof validParams.limit === 'number')
    
    // 测试缺少必需参数
    try {
      ParameterValidator.validate('getProject', {})
      console.log('❌ 应该抛出缺少必需参数错误')
    } catch (error) {
      console.log('✅ 正确捕获缺少必需参数错误:', error.message)
    }
    
  } catch (error) {
    console.error('参数验证测试失败:', error.message)
  }
}

// 测试用例 4: 智能执行计划生成测试
async function testIntelligentExecutionPlanner() {
  console.log('\n=== 测试智能执行计划生成器 ===')
  
  try {
    // 测试默认计划生成（当AI调用失败时）
    const plan = IntelligentExecutionPlanner.generateDefaultPlan('查看okr项目的任务', 'find_task')
    
    console.log('默认执行计划:')
    console.log('- 计划 ID:', plan.planId)
    console.log('- 总步骤数:', plan.totalSteps)
    console.log('- 预估总时间:', plan.estimatedTotalTime)
    console.log('- 步骤详情:', plan.steps.map(s => ({
      toolName: s.toolName,
      description: s.description,
      hasDynamicParams: JSON.stringify(s.parameters).includes('$context')
    })))
    
    console.log('✅ 默认计划生成成功')
    
  } catch (error) {
    console.error('智能执行计划生成测试失败:', error.message)
  }
}

// 测试用例 5: 工具注册表测试
function testToolRegistry() {
  console.log('\n=== 测试工具注册表 ===')
  
  console.log('已注册的工具:')
  for (const [toolName, config] of Object.entries(TOOL_REGISTRY)) {
    console.log(`- ${toolName}: ${config.description}`)
    console.log(`  云函数: ${config.cloudFunction}`)
    console.log(`  方法: ${config.method}`)
    console.log(`  预估时间: ${config.metadata.estimatedTime}ms`)
  }
  
  console.log('✅ 工具注册表检查完成')
}

// 测试用例 6: 路径提取测试
function testPathExtraction() {
  console.log('\n=== 测试路径提取功能 ===')
  
  const testData = {
    projects: [
      { id: 'proj-1', name: 'OKR项目' },
      { id: 'proj-2', name: '日常任务' }
    ],
    tasks: [
      { id: 'task-1', title: '制定目标' }
    ]
  }
  
  // 测试简单路径
  const result1 = DynamicParameterResolver.extractValueByPath(testData, 'projects')
  console.log('简单路径提取:', result1.length === 2)
  
  // 测试数组索引
  const result2 = DynamicParameterResolver.extractValueByPath(testData, 'projects[0].name')
  console.log('数组索引提取:', result2 === 'OKR项目')
  
  // 测试数组筛选
  const result3 = DynamicParameterResolver.extractValueByPath(testData, 'projects[name=okr].id')
  console.log('数组筛选提取:', result3 === 'proj-1')
  
  console.log('✅ 路径提取测试完成')
}

// 运行所有测试
async function runAllV12Tests() {
  console.log('开始 V1.2 动态参数解析功能测试...\n')
  
  try {
    await testDynamicParameterResolver()
    await testFilterExpression()
    testParameterValidator()
    await testIntelligentExecutionPlanner()
    testToolRegistry()
    testPathExtraction()
    
    console.log('\n✅ V1.2 所有测试完成！')
    console.log('\n🎉 动态参数解析功能已成功实现：')
    console.log('- ✅ 动态参数解析器 (DynamicParameterResolver)')
    console.log('- ✅ 智能执行计划生成器 (IntelligentExecutionPlanner)')
    console.log('- ✅ 参数验证器 (ParameterValidator)')
    console.log('- ✅ 工具注册表 (TOOL_REGISTRY)')
    console.log('- ✅ 真实工具调用引擎 (callRealTool)')
    console.log('- ✅ 智能执行引擎 (executeIntelligentPlan)')
    
  } catch (error) {
    console.error('\n❌ V1.2 测试失败：', error.message)
    console.error(error.stack)
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runAllV12Tests()
}

module.exports = {
  testDynamicParameterResolver,
  testFilterExpression,
  testParameterValidator,
  testIntelligentExecutionPlanner,
  testToolRegistry,
  testPathExtraction,
  runAllV12Tests
}
